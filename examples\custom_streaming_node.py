"""
Example of creating custom streaming events in LangGraph nodes.

This shows how to emit custom events during node execution that can be
captured by streaming listeners.
"""

from typing import Dict, Any
from langchain_core.messages import AIMessage, ToolMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.callbacks import CallbackManagerFor<PERSON>hainRun
from agent.state import State


class StreamingToolNode:
    """Enhanced tool node with custom streaming events."""
    
    def __init__(self, tools):
        self.tools = tools
        self.tools_by_name = {tool.name: tool for tool in tools}
    
    async def ainvoke(self, state: State, config: RunnableConfig) -> Dict[str, Any]:
        """Execute tools with custom streaming events."""
        messages = state.get("messages", [])
        if not messages:
            return {"messages": []}

        last_message = messages[-1]
        if not isinstance(last_message, AIMessage) or not last_message.tool_calls:
            return {"messages": []}

        tool_messages = []
        
        # Get callback manager for emitting events
        callback_manager = config.get("callbacks")
        
        for tool_call in last_message.tool_calls:
            tool_name = tool_call["name"]
            tool_args = tool_call.get("args", {})
            tool_id = tool_call["id"]
            
            # Emit custom event: Tool execution starting
            if callback_manager:
                await callback_manager.on_text(
                    f"🔧 Starting tool execution: {tool_name}",
                    verbose=True
                )
            
            try:
                # Get the tool
                tool = self.tools_by_name.get(tool_name)
                if not tool:
                    raise ValueError(f"Tool {tool_name} not found")
                
                # Emit custom event: Tool found and ready
                if callback_manager:
                    await callback_manager.on_text(
                        f"✅ Tool {tool_name} found, executing with args: {tool_args}",
                        verbose=True
                    )
                
                # Execute the tool
                result = await tool.ainvoke(tool_args)
                
                # Emit custom event: Tool execution completed
                if callback_manager:
                    await callback_manager.on_text(
                        f"🎉 Tool {tool_name} completed successfully",
                        verbose=True
                    )
                
                # Create tool message
                tool_message = ToolMessage(
                    content=str(result),
                    tool_call_id=tool_id
                )
                tool_messages.append(tool_message)
                
            except Exception as e:
                # Emit custom event: Tool execution failed
                if callback_manager:
                    await callback_manager.on_text(
                        f"❌ Tool {tool_name} failed: {str(e)}",
                        verbose=True
                    )
                
                # Create error message
                error_message = ToolMessage(
                    content=f"Error executing {tool_name}: {str(e)}",
                    tool_call_id=tool_id
                )
                tool_messages.append(error_message)
        
        return {"messages": tool_messages}


# Example of using the streaming tool node
async def example_with_custom_events():
    """Example showing custom streaming events."""
    from langchain_core.callbacks import AsyncCallbackHandler
    from agent.graph import graph
    from langchain_core.messages import HumanMessage
    
    class CustomStreamingHandler(AsyncCallbackHandler):
        """Custom callback handler to capture streaming events."""
        
        async def on_text(self, text: str, **kwargs) -> None:
            """Handle text events (our custom streaming messages)."""
            print(f"📡 STREAM EVENT: {text}")
        
        async def on_tool_start(self, serialized: Dict[str, Any], input_str: str, **kwargs) -> None:
            """Handle tool start events."""
            tool_name = serialized.get("name", "unknown")
            print(f"🚀 TOOL START: {tool_name}")
        
        async def on_tool_end(self, output: str, **kwargs) -> None:
            """Handle tool end events."""
            print(f"🏁 TOOL END: Output length {len(output)} chars")
    
    # Configuration with custom callback
    config = {
        "configurable": {
            "thread_id": "custom_streaming_789",
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful assistant."
        },
        "callbacks": [CustomStreamingHandler()]
    }
    
    inputs = {
        "messages": [HumanMessage(content="Find aperitivo deals")],
        "session_id": "custom_streaming_789",
        "user_id": "fe95e629-0a4e-474b-97d1-fafe9d6863e3",
        "latitude": "45.4666",
        "longitude": "9.1832",
        "memory_lenght": "15"
    }
    
    print("🎬 Starting execution with custom streaming events...")
    
    # Execute with streaming
    async for chunk in graph.astream(inputs, config):
        if "messages" in chunk:
            print(f"📦 Received chunk with {len(chunk['messages'])} messages")


if __name__ == "__main__":
    import asyncio
    asyncio.run(example_with_custom_events())
