"""
Example of streaming agent execution with tool invocation status.

This shows how to use LangGraph's built-in streaming capabilities to get
real-time updates during agent execution, including tool calls.
"""

import asyncio
from langchain_core.messages import HumanMessage
from agent.graph import graph


async def stream_agent_example():
    """Example of streaming agent execution with tool status updates."""
    
    # Configuration
    config = {
        "configurable": {
            "thread_id": "streaming_example_123",
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful customer service assistant for a marketplace."
        }
    }
    
    # Input state
    inputs = {
        "messages": [HumanMessage(content="Find me some aperitivo deals in Milan")],
        "session_id": "streaming_example_123",
        "user_id": "fe95e629-0a4e-474b-97d1-fafe9d6863e3",
        "latitude": "45.4666",
        "longitude": "9.1832",
        "memory_lenght": "15"
    }
    
    print("🚀 Starting streaming agent execution...")
    print("=" * 50)
    
    # Stream the execution
    async for chunk in graph.astream(inputs, config, stream_mode="values"):
        # Get the latest messages from the chunk
        if "messages" in chunk and chunk["messages"]:
            latest_message = chunk["messages"][-1]
            
            # Handle different message types
            if hasattr(latest_message, 'type'):
                if latest_message.type == "ai":
                    if hasattr(latest_message, 'tool_calls') and latest_message.tool_calls:
                        # Tool call detected
                        for tool_call in latest_message.tool_calls:
                            print(f"🔧 Invoking tool: {tool_call['name']}")
                            print(f"   Arguments: {tool_call.get('args', {})}")
                    elif latest_message.content:
                        # AI response content
                        print(f"🤖 AI: {latest_message.content}")
                        
                elif latest_message.type == "tool":
                    # Tool execution result
                    print(f"✅ Tool result received")
                    print(f"   Content length: {len(latest_message.content)} chars")
    
    print("=" * 50)
    print("✨ Streaming completed!")


async def stream_with_detailed_events():
    """Example using different stream modes for more detailed events."""
    
    config = {
        "configurable": {
            "thread_id": "detailed_streaming_456",
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful customer service assistant."
        }
    }
    
    inputs = {
        "messages": [HumanMessage(content="Show me restaurant deals near me")],
        "session_id": "detailed_streaming_456",
        "user_id": "fe95e629-0a4e-474b-97d1-fafe9d6863e3",
        "latitude": "45.4666",
        "longitude": "9.1832",
        "memory_lenght": "15"
    }
    
    print("🔍 Detailed streaming with events...")
    print("=" * 50)
    
    # Stream with events mode to see node transitions
    async for event in graph.astream_events(inputs, config, version="v1"):
        event_type = event.get("event")
        name = event.get("name", "")
        
        if event_type == "on_chain_start":
            if name in ["call_model", "tools"]:
                print(f"🏁 Starting node: {name}")
                
        elif event_type == "on_chain_end":
            if name in ["call_model", "tools"]:
                print(f"🏁 Finished node: {name}")
                
        elif event_type == "on_tool_start":
            tool_name = event.get("name", "unknown")
            print(f"🔧 Tool starting: {tool_name}")
            
        elif event_type == "on_tool_end":
            tool_name = event.get("name", "unknown")
            print(f"✅ Tool completed: {tool_name}")
    
    print("=" * 50)
    print("✨ Detailed streaming completed!")


if __name__ == "__main__":
    print("Choose streaming example:")
    print("1. Basic streaming with tool status")
    print("2. Detailed streaming with events")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        asyncio.run(stream_agent_example())
    elif choice == "2":
        asyncio.run(stream_with_detailed_events())
    else:
        print("Invalid choice. Running basic example...")
        asyncio.run(stream_agent_example())
